import { createContext, useContext, useEffect, useState } from 'react';
// import { Address } from 'viem';
import { useAccount } from 'wagmi';

import { Membership } from '@/class/interface/presale';

import { PresaleContext } from './presale-provider';

interface MembershipsContextProps {
  memberships: Membership[];
  selectedMembershipId: string;
  handleMembershipChange: (membershipId: string) => void;
  fetchMemberships: () => void;
  loading: boolean;
  error: string | null;
}

export const MembershipsContext = createContext<MembershipsContextProps>({
  memberships: [],
  selectedMembershipId: '',
  handleMembershipChange: () => {},
  fetchMemberships: () => {},
  loading: true,
  error: null,
});

interface MembershipsProviderProps {
  children: React.ReactNode;
}

export const MembershipsProvider = ({ children }: MembershipsProviderProps) => {
  const { presaleInstance, selectedRoundId, handleRoundChange } =
    useContext(PresaleContext);

  const { address } = useAccount();

  const [selectedMembershipId, setSelectedMembershipId] = useState<string>('');

  const [memberships, setMemberships] = useState<Membership[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const handleMembershipChange = (membershipId: string) => {
    setSelectedMembershipId(membershipId);
  };

  const fetchMemberships = async () => {
    if (!presaleInstance || !address) {
      console.log('MembershipsProvider: Cannot fetch memberships - missing presale instance or address');
      setLoading(false);
      setError(null);
      setMemberships([]);
      setSelectedMembershipId('');
      return;
    }

    try {
      console.log('MembershipsProvider: Fetching memberships for address:', address);
      setLoading(true);
      setError(null);

      const memberships = await presaleInstance.getMemberships(address);
      console.log('MembershipsProvider: Memberships fetched:', memberships.length, 'found');

      if (memberships.length <= 0) {
        console.log('MembershipsProvider: No memberships found for address');
        setSelectedMembershipId('');
        setMemberships([]);
        setLoading(false);
        return;
      }

      const roundMemberships = memberships.filter((m) => m.roundId === selectedRoundId);
      console.log('MembershipsProvider: Round memberships for round', selectedRoundId, ':', roundMemberships.length);

      if (
        roundMemberships.length > 0 &&
        (!selectedMembershipId ||
          !roundMemberships.some((m) => m.id === selectedMembershipId))
      ) {
        console.log('MembershipsProvider: Selecting first round membership:', roundMemberships[0].id);
        handleMembershipChange(roundMemberships[0].id);
      }

      if (
        roundMemberships.length <= 0 &&
        (!selectedMembershipId || !memberships.some((m) => m.id === selectedMembershipId))
      ) {
        console.log('MembershipsProvider: No round memberships, switching to round:', memberships[0].roundId);
        handleRoundChange(memberships[0].roundId);
        handleMembershipChange(memberships[0].id);
      }

      setMemberships(memberships);
      setLoading(false);
    } catch (err) {
      const errorMessage = `Failed to fetch memberships: ${err instanceof Error ? err.message : 'Unknown error'}`;
      console.error('MembershipsProvider: Error fetching memberships:', err);
      setError(errorMessage);
      setMemberships([]);
      setSelectedMembershipId('');
      setLoading(false);
    }
  };

  useEffect(() => {
    if (presaleInstance && address) {
      fetchMemberships();
    } else {
      // Reset state when dependencies are not available
      setLoading(false);
      setError(null);
      setMemberships([]);
      setSelectedMembershipId('');
    }
  }, [presaleInstance, address, selectedRoundId]);

  return (
    <MembershipsContext.Provider
      value={{
        memberships,
        selectedMembershipId,
        handleMembershipChange,
        fetchMemberships,
        loading,
        error,
      }}
    >
      {children}
    </MembershipsContext.Provider>
  );
};
