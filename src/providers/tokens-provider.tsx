import { createContext, useContext, useEffect, useState } from 'react';
import { Address, erc20Abi, getContract, maxInt256, zeroAddress } from 'viem';
import { useAccount } from 'wagmi';

import { ProjectConfigContext } from './project-config-provider';
import { PublicClientContext } from './public-client-provider';

export type Token = {
  address: Address;
  symbol: string;
  decimals: number;
  balance: string;
  allowance?: string;
  url?: string;
};

export interface TokensContextProps {
  vestedToken: Token;
  collectedToken: Token;
  fetchVestedTokenData: () => void;
  fetchCollectedTokenData: () => void;
  loading: boolean;
  error: string | null;
}

export const TokensContext = createContext<TokensContextProps>({
  vestedToken: {
    address: zeroAddress,
    symbol: '',
    balance: '0',
    decimals: 18,
  },
  collectedToken: {
    address: zeroAddress,
    symbol: '',
    balance: '0',
    decimals: 18,
  },
  fetchVestedTokenData: () => {},
  fetchCollectedTokenData: () => {},
  loading: true,
  error: null,
});

interface TokensProviderProps {
  children: React.ReactNode;
}

export const TokensProvider = ({ children }: TokensProviderProps) => {
  const { vestedToken, collectedToken, presaleAddress } =
    useContext(ProjectConfigContext);
  const { publicClient } = useContext(PublicClientContext);
  const { address: walletAddress } = useAccount();

  const [vestedTokenData, setVestedTokenData] = useState<Token>({
    ...vestedToken,
    balance: '0',
  });

  const [collectedTokenData, setCollectedTokenData] = useState<Token>({
    ...collectedToken,
    balance: '0',
    allowance: '0',
  });

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchVestedTokenData = async () => {
    if (!walletAddress || !publicClient) {
      console.log('TokensProvider: Cannot fetch vested token data - missing wallet address or public client');
      return;
    }

    try {
      console.log('TokensProvider: Fetching vested token data for address:', walletAddress);
      setError(null);

      const vestedTokenContract = getContract({
        client: publicClient,
        address: vestedToken.address,
        abi: erc20Abi,
      });

      const balance = await vestedTokenContract.read.balanceOf([walletAddress]);
      console.log('TokensProvider: Vested token balance fetched:', balance.toString());

      const _vestedToken = {
        ...vestedToken,
        balance: balance.toString(),
      };

      setVestedTokenData(_vestedToken);
    } catch (err) {
      const errorMessage = `Failed to fetch vested token balance: ${err instanceof Error ? err.message : 'Unknown error'}`;
      console.error('TokensProvider: Error fetching vested token data:', err);
      setError(errorMessage);

      // Reset to default state on error
      setVestedTokenData({
        ...vestedToken,
        balance: '0',
      });
    }
  };

  const fetchCollectedTokenData = async () => {
    if (!walletAddress || !publicClient) {
      console.log('TokensProvider: Cannot fetch collected token data - missing wallet address or public client');
      return;
    }

    try {
      console.log('TokensProvider: Fetching collected token data for address:', walletAddress);
      setError(null);

      if (collectedToken.isNative) {
        const balance = await publicClient.getBalance({ address: walletAddress });
        console.log('TokensProvider: Native token balance fetched:', balance.toString());

        const _collectedToken = {
          ...collectedToken,
          balance: balance.toString(),
          allowance: maxInt256.toString(),
        };

        setCollectedTokenData(_collectedToken);
        return;
      }

      const collectedTokenContract = getContract({
        client: publicClient,
        address: collectedToken.address,
        abi: erc20Abi,
      });

      const [balance, allowance] = await Promise.all([
        collectedTokenContract.read.balanceOf([walletAddress]),
        collectedTokenContract.read.allowance([walletAddress, presaleAddress]),
      ]);

      console.log('TokensProvider: Collected token balance fetched:', balance.toString());
      console.log('TokensProvider: Collected token allowance fetched:', allowance.toString());

      const _collectedToken = {
        ...collectedToken,
        balance: balance.toString(),
        allowance: allowance.toString(),
      };

      setCollectedTokenData(_collectedToken);
    } catch (err) {
      const errorMessage = `Failed to fetch collected token data: ${err instanceof Error ? err.message : 'Unknown error'}`;
      console.error('TokensProvider: Error fetching collected token data:', err);
      setError(errorMessage);

      // Reset to default state on error
      setCollectedTokenData({
        ...collectedToken,
        balance: '0',
        allowance: '0',
      });
    }
  };

  const fetchTokensData = async () => {
    if (!walletAddress || !publicClient) {
      console.log('TokensProvider: Cannot fetch token data - missing wallet address or public client');
      setLoading(false);
      return;
    }

    try {
      console.log('TokensProvider: Starting token data fetch for address:', walletAddress);
      setLoading(true);
      setError(null);

      await Promise.all([fetchVestedTokenData(), fetchCollectedTokenData()]);
      console.log('TokensProvider: Token data fetch completed successfully');
    } catch (err) {
      const errorMessage = `Failed to fetch token data: ${err instanceof Error ? err.message : 'Unknown error'}`;
      console.error('TokensProvider: Error in fetchTokensData:', err);
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (walletAddress && publicClient) {
      fetchTokensData();
    } else {
      // Reset state when wallet disconnected
      setLoading(false);
      setError(null);
      setVestedTokenData({
        ...vestedToken,
        balance: '0',
      });
      setCollectedTokenData({
        ...collectedToken,
        balance: '0',
        allowance: '0',
      });
    }
  }, [walletAddress, publicClient]);

  return (
    <TokensContext.Provider
      value={{
        vestedToken: vestedTokenData,
        collectedToken: collectedTokenData,
        fetchVestedTokenData,
        fetchCollectedTokenData,
        loading,
        error,
      }}
    >
      {children}
    </TokensContext.Provider>
  );
};
