import { useContext, useEffect, useState } from 'react';

import { PresaleRoundState } from '@/class/interface/presale';
import { MembershipsContext } from '@/providers/membership-provider';
import { OldMembershipsContext } from '@/providers/old-membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';

import BuyTokensPanel from './buy-tokens-panel/buy-tokens-panel';
import ExtendAllocationPanel from './extend-allocation-panel/extend-allocation-panel';
import { Spinner } from './icons/spinner';
import AddToMetamask from './left-panel/add-to-metamask';
import { MembershipSelect } from './left-panel/membership-select';
import MigrateButton from './left-panel/migrate-button';
import RoundDescription from './left-panel/round-description';
import { RoundSelect } from './left-panel/round-select';
import RoundTimings from './left-panel/round-timings';
import { TransferMembership } from './left-panel/transfer-membership';
import NoMemberships from './no-memberships';
import VestPanel from './vest-panel/vest-panel';

export default function VestLayout() {
  // Contexts
  const { vestedToken } = useContext(ProjectConfigContext);

  const { presaleData, selectedRoundId, handleRoundChange } = useContext(PresaleContext);

  const { memberships, selectedMembershipId, handleMembershipChange, loading } =
    useContext(MembershipsContext);

  const { memberships: _oldMemberships, loading: _oldLoading } =
    useContext(OldMembershipsContext);

  const [presaleRounds, setPresaleRounds] = useState(
    presaleData?.rounds.filter((round) =>
      memberships.some((membership) => membership.roundId === round.roundId),
    ) ?? [],
  );

  const [selectedRound, setSelectedRound] = useState(
    presaleRounds.find((round) => round.roundId === selectedRoundId),
  );
  const [roundMemberships, setRoundMemberships] = useState(
    memberships.filter((membership) => membership.roundId === selectedRoundId),
  );
  const [selectedMembership, setSelectedMembership] = useState(
    memberships.find((membership) => membership.id === selectedMembershipId),
  );

  useEffect(() => {
    const _presaleRounds =
      presaleData?.rounds.filter((round) =>
        memberships.some((membership) => membership.roundId === round.roundId),
      ) ?? [];

    const _selectedRound = _presaleRounds.find(
      (round) => round.roundId === selectedRoundId,
    );
    const _roundMemberships = memberships.filter(
      (membership) => membership.roundId === selectedRoundId,
    );

    const _selectedMembership = memberships.find(
      (membership) => membership.id === selectedMembershipId,
    );

    setPresaleRounds(_presaleRounds);
    setSelectedRound(_selectedRound);
    setRoundMemberships(_roundMemberships);
    setSelectedMembership(_selectedMembership);
  }, [memberships, selectedRoundId, selectedMembershipId]);

  return !presaleData || loading ? (
    <Spinner className="animate-spin w-12 h-12" />
  ) : (
    <div className="xl:grid xl:grid-cols-3 space-y-4 gap-8 w-full my-8">
      <div className="space-y-12">
        <h2 className="text-3xl sm:text-5xl font-semibold flex flex-col items-start gap-2">
          <span className="text-secondary">{vestedToken.name}</span>
          <span>Vest Portal</span>
        </h2>

        <div className="flex flex-col gap-2">
          <MigrateButton />
        </div>

        <div className="flex flex-col gap-4">
          {presaleRounds.length > 1 && (
            <RoundSelect
              rounds={presaleRounds}
              selectedRoundId={selectedRoundId}
              setSelectedRoundId={(value) => {
                handleRoundChange(value);
                handleMembershipChange(memberships.find((m) => m.roundId === value)!.id);
              }}
            />
          )}

          {roundMemberships.length > 1 && (
            <MembershipSelect
              memberships={roundMemberships}
              selectedMembershipId={selectedMembershipId}
              setSelectedMembershipId={(value) => handleMembershipChange(value)}
            />
          )}

          {roundMemberships.length > 0 && !selectedMembership?.proofs && (
            <TransferMembership />
          )}
        </div>

        {selectedRound && selectedMembership && roundMemberships.length > 0 && (
          <RoundDescription
            round={selectedRound}
            membership={selectedMembership}
            vestedToken={vestedToken}
          />
        )}

        {selectedRound && selectedMembership && roundMemberships.length > 0 && (
          <div className="flex flex-col gap-2">
            <RoundTimings round={selectedRound} membership={selectedMembership} />
          </div>
        )}

        <div className="flex flex-col gap-2">
          <AddToMetamask />
        </div>
      </div>

      <div className="col-span-2">
        {roundMemberships.length <= 0 && <NoMemberships />}

        {selectedMembership &&
          selectedRound &&
          (selectedRound.state === PresaleRoundState.vesting ||
            BigInt(selectedMembership.allocation) <=
              BigInt(selectedMembership.usage.max) + BigInt(10 * 10 ** 12)) && (
            <VestPanel roundData={selectedRound} membershipData={selectedMembership} />
          )}

        {selectedMembership &&
          selectedRound &&
          selectedRound.state !== PresaleRoundState.vesting &&
          BigInt(selectedMembership.allocation) >
            BigInt(selectedMembership.usage.max) + BigInt(10 * 10 ** 12) && (
            <BuyTokensPanel membership={selectedMembership} round={selectedRound} />
          )}

        {selectedMembership &&
          selectedRound &&
          selectedRound.state !== PresaleRoundState.vesting &&
          BigInt(selectedMembership.allocation) <=
            BigInt(selectedMembership.usage.max) + BigInt(10 * 10 ** 12) &&
          BigInt(selectedMembership.allocation) >
            BigInt(selectedMembership.usage.max) && (
            <ExtendAllocationPanel
              membership={selectedMembership}
              round={selectedRound}
            />
          )}
      </div>
    </div>
  );
}
