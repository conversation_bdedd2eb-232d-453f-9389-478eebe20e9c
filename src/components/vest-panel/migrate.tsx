import { getWalletClient } from '@wagmi/core';
import { useContext, useState } from 'react';
import { erc20Abi, formatUnits, getContract } from 'viem';
import { WagmiContext } from 'wagmi';

import { IPresale, Membership } from '@/class/interface/presale';
import { PresaleFactory } from '@/class/presale-factory';
import { cutDecimals } from '@/lib/cut-decimals';
import { MembershipsContext } from '@/providers/membership-provider';
import { PresaleContext } from '@/providers/presale-provider';
import { ProjectConfigContext } from '@/providers/project-config-provider';
import { PublicClientContext } from '@/providers/public-client-provider';
import { TokensContext } from '@/providers/tokens-provider';

import { Spinner } from '../icons/spinner';
import { Button } from '../ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  Dialog<PERSON>itle,
} from '../ui/dialog';

// Old presale contract configuration - now handled by OldMembershipsProvider
// These constants are moved to the provider for better separation of concerns
const OLD_PRESALE_CONTRACT_ADDRESS =
  '0x04aAC06e8f5f2D6f76177819C9fd69736FdbF7f2' as const;
const OLD_PRESALE_VERSION = 'v3' as const;

interface MigrateProps {
  membership: Membership;
}

/**
 * Migrate component enables users to migrate their funds from an old presale contract
 * to a new presale contract in a single transaction flow.
 *
 * The component performs three sequential operations:
 * 1. Claims back funds from the old presale contract (using membership data from old contract)
 * 2. Approves the new presale contract to spend the claimed tokens
 * 3. Automatically purchases tokens from the new presale contract
 *
 * Features:
 * - Uses membership data from OLD contract (via OldMembershipsProvider)
 * - User confirmation dialog with migration details
 * - Progress indicators for each transaction step
 * - Comprehensive error handling and user feedback
 * - Automatic data refresh after successful migration
 *
 * Note: This component expects to receive membership data from the OLD contract,
 * not the new one. The OldMembershipsProvider should be used to provide this data.
 */

export function Migrate({ membership }: MigrateProps) {
  // Context hooks
  const { fetchMemberships } = useContext(MembershipsContext);
  const { presaleInstance } = useContext(PresaleContext);
  const { chain, vestedToken, collectedToken } = useContext(ProjectConfigContext);
  const { fetchCollectedTokenData } = useContext(TokensContext);
  const config = useContext(WagmiContext);
  const { publicClient } = useContext(PublicClientContext);

  // State management
  const [isLoading, setIsLoading] = useState(false);
  const [migrationStep, setMigrationStep] = useState<
    'idle' | 'claiming' | 'approving' | 'purchasing' | 'completed' | 'error'
  >('idle');
  const [showPreMigrationConfirmation, setShowPreMigrationConfirmation] = useState(false);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [errorMessage, setErrorMessage] = useState<string>('');

  // Calculate claimable amount from old contract
  const claimableFromOldContract =
    (BigInt(BigInt(membership.usage.max)) * BigInt(membership.price)) /
    BigInt(10 ** vestedToken.decimals);

  // Calculate the vested token amount for full migration
  const vestedTokenAmountToMigrate = BigInt(membership.usage.max);

  // Handle migrate all button click
  const handleMigrateAll = async () => {
    setShowPreMigrationConfirmation(true);
  };

  // Perform the actual migration
  async function performMigration() {
    // Validation checks
    if (vestedTokenAmountToMigrate === 0n) {
      setErrorMessage('No funds available to migrate.');
      setMigrationStep('error');
      return;
    }
    const client = await getWalletClient(config!);
    if (!client) {
      setErrorMessage('Please connect your wallet to continue.');
      setMigrationStep('error');
      return;
    }

    setIsLoading(true);
    setErrorMessage('');

    try {
      // Step 1: Claim back from old contract
      setMigrationStep('claiming');

      // Create instance of old presale contract
      let oldPresaleInstance: IPresale;
      try {
        oldPresaleInstance = await PresaleFactory.createInstance(
          OLD_PRESALE_VERSION,
          publicClient,
          OLD_PRESALE_CONTRACT_ADDRESS,
        );
        console.log('üöÄ ~ performMigration ~ oldPresaleInstance:', oldPresaleInstance);
      } catch {
        throw new Error(
          'Failed to connect to old presale contract. Please check the contract address.',
        );
      }

      // Claim back tokens from old contract
      const claimTxHash = await oldPresaleInstance.claimBackTokens(
        client,
        chain,
        membership.id,
        BigInt(vestedTokenAmountToMigrate),
      );

      // Wait for claim transaction to complete
      await publicClient.waitForTransactionReceipt({
        hash: claimTxHash,
      });

      // Step 2: Approve tokens for new contract
      setMigrationStep('approving');

      // Validate presale instance exists for approval
      if (!presaleInstance) {
        throw new Error(
          'New presale contract not available. Please refresh the page and try again.',
        );
      }

      // Create ERC20 contract instance for the collected token
      const collectedTokenContract = getContract({
        address: collectedToken.address,
        abi: erc20Abi,
        client: {
          public: publicClient,
          wallet: client,
        },
      });

      // Approve the new presale contract to spend the claimed tokens
      const approveTxHash = await collectedTokenContract.write.approve(
        [
          presaleInstance.getPresaleData().presaleContractAddress,
          claimableFromOldContract,
        ],
        {
          account: client.account,
          chain,
        },
      );

      // Wait for approval transaction to complete
      await publicClient.waitForTransactionReceipt({
        hash: approveTxHash,
      });

      // Step 3: Purchase from new contract
      setMigrationStep('purchasing');

      // Purchase tokens from new contract using the current presale instance
      const purchaseTxHash = await presaleInstance.buyTokens(
        client,
        chain,
        membership,
        BigInt(vestedTokenAmountToMigrate),
      );

      // Wait for purchase transaction to complete
      await publicClient.waitForTransactionReceipt({
        hash: purchaseTxHash,
      });

      // Step 4: Complete migration
      setMigrationStep('completed');

      // Refresh data
      fetchCollectedTokenData();
      fetchMemberships();

      // Reset state after a short delay
      setTimeout(() => {
        setShowConfirmation(false);
        setMigrationStep('idle');
      }, 2000);
    } catch (error) {
      console.error('Migration failed:', error);
      setMigrationStep('error');

      // Determine error message based on migration step
      let userErrorMessage = 'Migration failed. Please try again.';

      if (migrationStep === 'claiming') {
        userErrorMessage =
          'Failed to claim funds from old contract. Please check your transaction and try again.';
      } else if (migrationStep === 'approving') {
        userErrorMessage =
          'Claiming succeeded but token approval failed. Please try approving manually and then purchase.';
      } else if (migrationStep === 'purchasing') {
        userErrorMessage =
          'Claiming and approval succeeded but purchase from new contract failed. Your funds may be available to claim manually.';
      }

      // Handle specific error types
      if (error instanceof Error) {
        if (error.message.includes('User rejected')) {
          userErrorMessage = 'Transaction was cancelled by user.';
        } else if (error.message.includes('insufficient funds')) {
          userErrorMessage = 'Insufficient funds for gas fees.';
        } else if (error.message.includes('execution reverted')) {
          userErrorMessage =
            'Transaction failed. Please check contract conditions and try again.';
        }
      }

      setErrorMessage(userErrorMessage);

      // Reset to idle after showing error for a few seconds
      setTimeout(() => {
        setMigrationStep('idle');
        setErrorMessage('');
      }, 5000);
    } finally {
      setIsLoading(false);
    }
  }

  // Handle confirmation cancel
  const handleConfirmationCancel = () => {
    setShowConfirmation(false);
    setErrorMessage('');
    setMigrationStep('idle');
  };

  return (
    <Card className="row-span-1">
      <CardHeader>
        <CardTitle>Migrate</CardTitle>
      </CardHeader>
      <CardContent className="h-full">
        {/* Pre-Migration Confirmation Dialog */}
        <Dialog
          open={showPreMigrationConfirmation}
          onOpenChange={(open) => {
            if (!open) {
              setShowPreMigrationConfirmation(false);
            }
          }}
        >
          <DialogContent className="bg-black text-white max-w-2xl">
            <DialogHeader>
              <DialogTitle>Migration Information</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-4 bg-blue-900/20 rounded-lg border border-blue-700">
                <h4 className="text-sm font-medium text-blue-400 mb-3">
                  Migration Process
                </h4>
                <p className="text-sm text-gray-300 mb-4">
                  NOTE: Once you click migrate, you need to complete 3 transactions on
                  MetaMask/your wallet to confirm your migration to the new presale smart
                  contract.
                </p>
                <p className="text-sm text-gray-300 mb-4">
                  After migration is completed, you can connect your TGLP subscriber
                  wallet to the new presale round at{' '}
                  <a
                    href="https://presale.raiinmaker.com/"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-blue-400 hover:text-blue-300 underline"
                  >
                    https://presale.raiinmaker.com/
                  </a>{' '}
                  to check your allocation, purchases, etc.
                </p>
              </div>

              {claimableFromOldContract > 0n ? (
                <div className="p-4 bg-green-900/20 rounded-lg border border-green-700">
                  <h4 className="text-sm font-medium text-green-400 mb-2">
                    Migration Available
                  </h4>
                  <p className="text-sm text-gray-300">
                    You have funds available for migration. Click &quot;Proceed with
                    Migration&quot; to continue.
                  </p>
                </div>
              ) : (
                <div className="p-4 bg-yellow-900/20 rounded-lg border border-yellow-700">
                  <h4 className="text-sm font-medium text-yellow-400 mb-2">
                    No Migration Available
                  </h4>
                  <p className="text-sm text-gray-300">
                    No funds are available for migration from the old contract.
                  </p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button
                variant={'outline'}
                onClick={() => setShowPreMigrationConfirmation(false)}
              >
                {claimableFromOldContract > 0n ? 'Cancel' : 'Close'}
              </Button>

              {claimableFromOldContract > 0n && (
                <Button
                  variant={'default'}
                  onClick={() => {
                    setShowPreMigrationConfirmation(false);
                    setShowConfirmation(true);
                  }}
                >
                  Proceed with Migration
                </Button>
              )}
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* Existing Migration Confirmation Dialog */}
        <Dialog
          open={showConfirmation}
          onOpenChange={(open) => {
            if (!open) {
              setMigrationStep('idle');
              setShowConfirmation(false);
              setErrorMessage('');
            }
          }}
        >
          <Button
            variant={'secondary'}
            onClick={handleMigrateAll}
            disabled={claimableFromOldContract === 0n || isLoading}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Spinner className="animate-spin mr-2" />
                {migrationStep === 'claiming' && 'Claiming from old contract...'}
                {migrationStep === 'approving' && 'Approving tokens...'}
                {migrationStep === 'purchasing' && 'Purchasing from new contract...'}
                {migrationStep === 'idle' && 'Processing...'}
              </>
            ) : (
              'Migrate All Funds'
            )}
          </Button>

          <DialogContent className="bg-black text-white">
            <DialogHeader>
              <DialogTitle>Confirm Migration</DialogTitle>
            </DialogHeader>

            <div className="space-y-4">
              <div className="p-4 bg-red-900/20 rounded-lg border border-red-700">
                <h4 className="text-sm font-medium text-red-400 mb-2">
                  Confirm Migration
                </h4>
                <p className="text-sm text-gray-300 mb-3">
                  You are about to migrate{' '}
                  <span className="font-semibold text-white">
                    {cutDecimals(
                      formatUnits(claimableFromOldContract, collectedToken.decimals),
                      2,
                    )}{' '}
                    {collectedToken.symbol}
                  </span>{' '}
                  from the old presale contract to the new one.
                </p>
                <div className="space-y-2 text-xs text-gray-300">
                  <p>
                    <strong>Step 1:</strong> Claim back all available{' '}
                    {collectedToken.symbol} from old contract
                  </p>
                  <p>
                    <strong>Step 2:</strong> Approve new contract to spend claimed tokens
                  </p>
                  <p>
                    <strong>Step 3:</strong> Purchase tokens in new contract with claimed
                    funds
                  </p>
                </div>
                <p className="text-xs text-yellow-400 mt-3">
                  This action cannot be undone. Please ensure you have sufficient gas for
                  all three transactions.
                </p>
              </div>

              {migrationStep !== 'idle' && (
                <div className="p-4 bg-blue-900/20 rounded-lg border border-blue-700">
                  <h4 className="text-sm font-medium text-blue-400 mb-2">
                    Migration Progress
                  </h4>
                  <div className="space-y-2">
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'claiming' ? 'text-yellow-400' : migrationStep === 'approving' || migrationStep === 'purchasing' || migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'claiming' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {(migrationStep === 'approving' ||
                        migrationStep === 'purchasing' ||
                        migrationStep === 'completed') && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Claiming from old contract</span>
                    </div>
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'approving' ? 'text-yellow-400' : migrationStep === 'purchasing' || migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'approving' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {(migrationStep === 'purchasing' ||
                        migrationStep === 'completed') && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Approving tokens</span>
                    </div>
                    <div
                      className={`flex items-center space-x-2 ${migrationStep === 'purchasing' ? 'text-yellow-400' : migrationStep === 'completed' ? 'text-green-400' : 'text-gray-400'}`}
                    >
                      {migrationStep === 'purchasing' && (
                        <Spinner className="animate-spin w-4 h-4" />
                      )}
                      {migrationStep === 'completed' && <span>✓</span>}
                      {migrationStep === 'error' && <span>✗</span>}
                      <span className="text-xs">Purchasing from new contract</span>
                    </div>
                  </div>
                </div>
              )}

              {migrationStep === 'error' && errorMessage && (
                <div className="p-4 bg-red-900/20 rounded-lg border border-red-700">
                  <h4 className="text-sm font-medium text-red-400 mb-2">
                    Migration Failed
                  </h4>
                  <p className="text-sm text-gray-300">{errorMessage}</p>
                </div>
              )}
            </div>

            <DialogFooter>
              <Button variant={'outline'} onClick={handleConfirmationCancel}>
                Cancel
              </Button>

              <Button
                variant={'destructive'}
                onClick={performMigration}
                disabled={isLoading}
                className="space-x-2"
              >
                {isLoading && <Spinner className="animate-spin" />}
                <span>
                  {migrationStep === 'claiming' && 'Claiming from old contract...'}
                  {migrationStep === 'approving' && 'Approving tokens...'}
                  {migrationStep === 'purchasing' && 'Purchasing from new contract...'}
                  {migrationStep === 'idle' && 'Confirm Migration'}
                  {migrationStep === 'completed' && 'Migration Complete'}
                  {migrationStep === 'error' && 'Try Again'}
                </span>
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardContent>
    </Card>
  );
}
