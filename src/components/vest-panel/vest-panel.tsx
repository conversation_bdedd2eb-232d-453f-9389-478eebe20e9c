import clsx from 'clsx';
import { useContext } from 'react';
import { useAccount } from 'wagmi';

import { Membership, Round } from '@/class/interface/presale';
import { MembershipsContext } from '@/providers/membership-provider';
import { TokensContext } from '@/providers/tokens-provider';

import { TgeTimer } from '../left-panel/tge-timer';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { ClaimBack } from './claim-back';
import { ClaimTokens } from './claim-tokens';
import { LockedBalance } from './locked-balance';
import { NextUnlock } from './next-unlock';
import { RememberFee } from './remember-fee';
import { TokensClaimed } from './tokens-claimed';
import { WalletDisconnect } from './wallet-disconnect';
import { WalletStatus } from './wallet-status';
import { YouOwn } from './you-own';

interface VestPanelProps {
  roundData: Round;
  membershipData: Membership;
}

export default function VestPanel({ membershipData }: VestPanelProps) {
  const { vestedToken, loading: _tokensLoading, error: tokensError } = useContext(TokensContext);
  const { loading: _membershipsLoading, error: membershipsError } = useContext(MembershipsContext);
  const { address, isConnected, chainId } = useAccount();

  const isRefundable =
    BigInt(membershipData.claimableBackUnit) > Date.now() / 1000 &&
    membershipData.usage.current === '0';

  // Show error state if there are critical errors
  if (tokensError || membershipsError) {
    return (
      <>
        <TgeTimer timestamp={membershipData.tgeStartTimestamp * 1000} />

        <div className="grid grid-cols-1 gap-4 w-full">
          <Card className="border-red-500 bg-red-50 dark:bg-red-950">
            <CardHeader>
              <CardTitle className="text-red-700 dark:text-red-300">Data Loading Error</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {tokensError && (
                  <div>
                    <p className="font-semibold text-red-600 dark:text-red-400">Token Data Error:</p>
                    <p className="text-sm text-red-600 dark:text-red-400">{tokensError}</p>
                  </div>
                )}
                {membershipsError && (
                  <div>
                    <p className="font-semibold text-red-600 dark:text-red-400">Membership Data Error:</p>
                    <p className="text-sm text-red-600 dark:text-red-400">{membershipsError}</p>
                  </div>
                )}
                <div className="mt-4 p-3 bg-gray-100 dark:bg-gray-800 rounded">
                  <p className="text-sm font-semibold text-gray-700 dark:text-gray-300">Debug Info:</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Wallet: {address || 'Not connected'}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Connected: {isConnected ? 'Yes' : 'No'}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Chain ID: {chainId || 'Unknown'}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Token Balance: {vestedToken.balance}</p>
                  <p className="text-xs text-gray-600 dark:text-gray-400">Token Address: {vestedToken.address}</p>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400 mt-4">
                  Please check your wallet connection and network settings. Try refreshing the page if the issue persists.
                </p>
              </div>
            </CardContent>
          </Card>
        </div>
      </>
    );
  }

  return (
    <>
      <TgeTimer timestamp={membershipData.tgeStartTimestamp * 1000} />



      <div className={'grid grid-cols-2 gap-4 xl:grid-cols-3 xl:grid-rows-4 w-full'}>
        <TokensClaimed
          current={membershipData.usage.current}
          max={membershipData.usage.max}
          tokenDecimals={vestedToken.decimals}
        />
        <div className="contents xl:grid xl:grid-cols-subgrid xl:row-span-4 xl:grid-rows-3 xl:gap-4 xl:grid-areas-panel-right-wide">
          <YouOwn
            balance={vestedToken.balance}
            symbol={vestedToken.symbol}
            decimals={vestedToken.decimals}
          />

          <div className="hidden xl:contents">
            <WalletStatus />
            <WalletDisconnect />
          </div>
        </div>

        <div className="xl:hidden col-span-2 grid grid-cols-2 gap-4 order-1">
          <WalletStatus />

          <WalletDisconnect />
        </div>

        <LockedBalance
          locked={membershipData.locked}
          tokenDecimals={vestedToken.decimals}
        />

        <div
          className={clsx(
            'grid row-span-2 col-span-2 xl:col-span-1 gap-4',
            isRefundable && 'xl:grid xl:row-span-3 xl:grid-rows-3',
          )}
        >
          <ClaimTokens membershipData={membershipData} />

          {isRefundable && <ClaimBack membership={membershipData} />}
        </div>

        <NextUnlock
          timestamp={membershipData.nextUnlockTimestamp}
          value={membershipData.nextUnlockValue}
          tokenDecimals={vestedToken.decimals}
        />

        <RememberFee isRefundable={isRefundable} />
      </div>
    </>
  );
}
