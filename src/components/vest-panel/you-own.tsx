import { useContext } from 'react';
import { formatUnits } from 'viem';

import { cutDecimals } from '@/lib/cut-decimals';
import { TokensContext } from '@/providers/tokens-provider';

import { WalletIcon } from '../icons';
import { Spinner } from '../icons/spinner';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

interface YouOwnProps {
  balance: string;
  symbol: string;
  decimals: number;
}

export const YouOwn = ({ balance, symbol, decimals }: YouOwnProps) => {
  const { loading, error } = useContext(TokensContext);

  return (
    <Card>
      <CardHeader>
        <CardTitle>You own</CardTitle>
      </CardHeader>
      <CardContent>
        <WalletIcon />
        {loading ? (
          <div className="flex flex-col gap-2 text-white">
            <Spinner className="animate-spin" />
            <span className="text-sm text-gray-400">Loading balance...</span>
          </div>
        ) : error ? (
          <div className="flex flex-col gap-2 text-red-400">
            <span className="text-lg font-semibold">Error</span>
            <span className="text-sm">Failed to load token balance</span>
            <span className="text-xs text-gray-500">{error}</span>
          </div>
        ) : balance === undefined ? (
          <Spinner className="animate-spin" />
        ) : (
          <div className="flex flex-col gap-2 text-white">
            <span className="text-2xl xl:text-4xl font-semibold">
              {cutDecimals(formatUnits(BigInt(balance), decimals), 2)}
            </span>
            <span className="text-xl xl:text-2xl font-semibold">{symbol}</span>
          </div>
        )}
      </CardContent>
    </Card>
  );
};
