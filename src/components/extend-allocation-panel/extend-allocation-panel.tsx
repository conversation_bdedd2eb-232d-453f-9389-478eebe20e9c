import { Membership, Round } from '@/class/interface/presale';

import ExtendAllocationForm from './extend-allocation-form';

interface ExtendAllocationPanelProps {
  membership: Membership;
  round: Round;
}

export default function ExtendAllocationPanel({
  membership,
  round,
}: ExtendAllocationPanelProps) {
  return (
    <div className="flex flex-col gap-4 w-full h-full">
      <ExtendAllocationForm membership={membership} round={round} />
    </div>
  );
}
